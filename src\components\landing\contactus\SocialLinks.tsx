import { Facebook, Instagram, Linkedin, MessageCircle } from "lucide-react";
import Link from "next/link";
import React from "react";

const socialLinks = [
  {
    name: "Instagram",
    href: "https://www.instagram.com/parhlai",
    icon: <Instagram />,
  },
  {
    name: "Facebook",
    href: "https://www.facebook.com/people/Parhlai/61558369453378/",
    icon: <Facebook />,
  },
  {
    name: "LinkedIn",
    href: "https://www.linkedin.com/company/parhlai",
    icon: <Linkedin />,
  },
  {
    name: "WhatsApp",
    href: "https://wa.me/923111111111",
    icon: <MessageCircle />,
  },
];

const SocialLinks = () => {
  return (
    <div className="py-8 sm:py-10">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">
            Got questions? We got answers.
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Message us, anywhere, anytime, for anything. We are here for you.
          </p>
        </div>
        <ul
          role="list"
          className="mx-auto mt-10 grid max-w-2xl grid-cols-2 gap-x-8 gap-y-16 text-center sm:grid-cols-3 lg:max-w-none lg:grid-cols-4"
        >
          {socialLinks.map((social) => (
            <li key={social.name}>
              <Link
                href={social.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex flex-col items-center"
              >
                {social.icon}
                <h3 className="mt-2 text-base font-semibold leading-7 tracking-tight text-gray-900">
                  {social.name}
                </h3>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default SocialLinks;
