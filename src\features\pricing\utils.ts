export type Anchor = {
  days: number;
  price: number;
};

const anchors: Anchor[] = [
  { days: 1, price: 99 },
  { days: 7, price: 600 },
  { days: 30, price: 1000 },
  { days: 60, price: 2000 },
  { days: 365, price: 6000 },
];

export function calculatePrice(days: number): {
  total: number;
  perDay: number;
} {
  // Exact match with an anchor point
  for (const anchor of anchors)
    if (days === anchor.days)
      return {
        total: anchor.price,
        perDay: Math.round(anchor.price / days),
      };

  // Find the two anchor points to interpolate between
  let lower: Anchor = anchors[0]!;
  let upper: Anchor = anchors[anchors.length - 1]!;

  for (let i = 0; i < anchors.length - 1; i++) {
    const a = anchors[i]!;
    const b = anchors[i + 1]!;
    if (days > a.days && days < b.days) {
      lower = a;
      upper = b;
      break;
    }
  }

  // Formula: Linear interpolation
  // ratio = (x - x1) / (x2 - x1)
  // y = y1 + ratio * (y2 - y1)
  // Now that lower and upper are guaranteed non-undefined, perform interpolation
  const ratio = (days - lower.days) / (upper.days - lower.days);
  const total = lower.price + ratio * (upper.price - lower.price);

  // Per-day cost
  const perDay = total / days;

  return {
    total: Math.round(total),
    perDay: Math.round(perDay),
  };
}
