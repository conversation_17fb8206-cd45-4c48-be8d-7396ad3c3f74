export type Grade = "A*" | "A" | "B" | "C" | "D" | "E" | "";
export type University = "NUST";
export type EducationSystem = "O / A Levels" | "Matric & FSC";
export type ActiveTab = "calculator" | "merit";
export type SubjectGrades<T extends string> = Record<T, Grade>;

export type OLevelGrades = SubjectGrades<
  | "subject1"
  | "subject2"
  | "subject3"
  | "subject4"
  | "subject5"
  | "subject6"
  | "subject7"
  | "subject8"
>;
export type ALevelGrades = SubjectGrades<
  | "subject1"
  | "subject2"
  | "subject3"
  | "subject4"
  | "subject5"
  | "subject6"
  | "subject7"
  | "subject8"
>;
export type ScoreData = {
  obtained: string;
  total: string;
};
