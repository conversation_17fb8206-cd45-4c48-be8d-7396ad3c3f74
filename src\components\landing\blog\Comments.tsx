"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { commentSchema, type CommentFormData } from "@/schemas/comment.schema";
import { useBlog } from "@/contexts/BlogContext";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type CommentsProps = {
  blogId: string;
};

const Comments = ({ blogId }: CommentsProps) => {
  const { comments, commentsError, addComment } = useBlog();

  const [submitting, setSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CommentFormData>({
    resolver: zodResolver(commentSchema),
  });

  // Comments are now loaded automatically when blog data is fetched
  // No need for separate useEffect to fetch comments

  // Handle form submission
  const onSubmit = async (data: CommentFormData) => {
    setSubmitting(true);
    setSuccessMessage(null);

    try {
      const success = await addComment({
        blogId,
        author: data.author,
        comment: data.comment,
      });

      if (success) {
        setSuccessMessage(
          "Comment submitted successfully! It may take a moment to appear.",
        );
        reset(); // Clear the form

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);
      }
    } catch (err) {
      console.error("Error submitting comment:", err);
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="mx-auto mt-16 max-w-7xl">
      {/* Divider */}
      <div className="mb-10 h-[3px] w-full bg-gray-200" />

      {/* Comments Header */}
      <h2 className="mb-8 text-2xl font-bold text-accent">
        Comments ({comments.length})
      </h2>

      {/* Comments List */}
      <div className="mb-12 space-y-6">
        {comments.length > 0 ? (
          comments.map((comment) => (
            <div key={comment._id} className="flex space-x-4">
              <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-full bg-gray-200">
                <div className="flex h-full w-full items-center justify-center text-lg font-semibold text-gray-600">
                  {comment.author.charAt(0).toUpperCase()}
                </div>
              </div>
              <div className="flex-1">
                <div className="mb-1 flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-black">
                      {comment.author}
                    </h4>
                    <p className="text-xs text-[#898989]">
                      {formatDate(comment.createdAt)}
                    </p>
                  </div>
                </div>
                <p className="text-sm leading-relaxed text-[#636363]">
                  {comment.comment}
                </p>
              </div>
            </div>
          ))
        ) : (
          <p className="py-8 text-center text-gray-500">
            No comments yet. Be the first to comment!
          </p>
        )}
      </div>

      {/* Leave a Reply Section */}
      <div>
        <h3 className="mb-6 text-xl font-bold text-accent">Leave a reply</h3>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 rounded-lg border border-green-200 bg-green-50 p-4">
            <p className="text-green-800">{successMessage}</p>
          </div>
        )}

        {/* Error Message */}
        {commentsError && (
          <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-red-800">{commentsError}</p>
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Comment Textarea */}
          <div>
            <textarea
              {...register("comment")}
              placeholder="Type your comment"
              rows={6}
              className={cn(
                "w-full rounded-lg border px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-1",
                errors.comment
                  ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:border-accent focus:ring-accent",
              )}
            />
            {errors.comment && (
              <p className="mt-1 text-sm text-red-600">
                {errors.comment.message}
              </p>
            )}
          </div>

          {/* Author Name Input */}
          <div>
            <input
              {...register("author")}
              type="text"
              placeholder="Your name"
              className={cn(
                "w-full rounded-lg border px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-1",
                errors.author
                  ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:border-accent focus:ring-accent",
              )}
            />
            {errors.author && (
              <p className="mt-1 text-sm text-red-600">
                {errors.author.message}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={submitting}
              className={cn(
                buttonVariants({ variant: "default", size: "lg" }),
                "w-full rounded-lg px-6 py-3 font-medium",
                submitting && "cursor-not-allowed opacity-50",
              )}
            >
              {submitting ? "Submitting..." : "Submit Comment"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Comments;
