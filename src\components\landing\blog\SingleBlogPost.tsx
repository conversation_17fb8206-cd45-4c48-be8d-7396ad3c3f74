"use client";

import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { FileIcon } from "@radix-ui/react-icons";
import { buttonVariants } from "@/components/ui/button";
import { cn, stripHtml } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import LoadingSpinner from "@/components/common/LoadingSpinner";
import Comments from "./Comments";

type SingleBlogPostProps = {
  slug: string;
};

const SingleBlogPost = ({ slug }: SingleBlogPostProps) => {
  const {
    currentBlog,
    currentBlogLoading,
    currentBlogError,
    fetchBlogBySlug,
    clearCurrentBlog,
  } = useBlog();

  useEffect(() => {
    if (slug) fetchBlogBySlug(slug);

    return () => {
      clearCurrentBlog();
    };
  }, [slug, fetchBlogBySlug, clearCurrentBlog]);

  if (currentBlogLoading)
    return (
      <div className="flex min-h-[70vh] w-full items-center justify-center">
        <LoadingSpinner width={60} height={60} />
      </div>
    );

  if (currentBlogError && !currentBlog)
    return (
      <div className="flex min-h-[70vh] w-full items-center justify-center">
        <div className="text-center">
          <p className="mb-4 text-red-500">{currentBlogError}</p>
          <Link
            href="/blog"
            className={cn(buttonVariants({ variant: "default" }), "")}
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    );

  // Use only API data
  if (!currentBlog && !currentBlogLoading)
    return (
      <div className="flex min-h-[70vh] w-full items-center justify-center">
        <div className="text-center">
          <p className="mb-4 text-xl text-gray-500">No Data</p>
          <Link
            href="/blog"
            className={cn(buttonVariants({ variant: "default" }), "")}
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    );

  // Normalize data structure for API data
  const post = currentBlog
    ? {
        id: currentBlog._id,
        title: currentBlog.title,
        excerpt:
          stripHtml(currentBlog.content).length > 200
            ? stripHtml(currentBlog.content).substring(0, 200).trim() + "..."
            : stripHtml(currentBlog.content),
        content: currentBlog.content,
        featuredImage:
          currentBlog.imageUrl || "/assets/images/free-nature-images.jpg",
        category: currentBlog.categories?.[0] || "General",
        tags: currentBlog.tags || [],
        date: new Date(currentBlog.publishedAt).toLocaleDateString(),
        readTime: currentBlog.readTime
          ? `${currentBlog.readTime} min read`
          : "5 min read",
        author: {
          name: currentBlog.author,
          bio: "Content Author",
        },
        slug: currentBlog.slug || currentBlog._id,
      }
    : null;

  if (!post)
    return (
      <div className="flex min-h-[70vh] w-full items-center justify-center">
        <div className="text-center">
          <p className="mb-4 text-xl text-gray-500">No Data</p>
          <Link
            href="/blog"
            className={cn(buttonVariants({ variant: "default" }), "")}
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen w-full">
      {/* Navigation Breadcrumb */}
      <div className="mx-auto px-[5%] pb-6">
        <nav className="mx-auto mb-16 flex max-w-[1408px] items-center space-x-2 text-lg font-semibold text-gray-400">
          <Link href="/blog" className="transition-colors hover:text-accent">
            Blogs
          </Link>
          <span>›</span>
          <span className="text-accent">{post.title}</span>
        </nav>

        {/* Header Section */}
        <div className="mx-auto mb-10 flex max-w-[1408px] flex-col lg:flex-row lg:gap-12">
          {/* Left Content */}
          <div className="lg:w-1/2">
            <h1 className="mb-4 text-4xl font-bold leading-tight text-accent">
              {post.title}
            </h1>

            <p className="mb-5 leading-relaxed text-black">{post.excerpt}</p>

            <div className="mb-6 flex items-center gap-6 text-sm font-semibold text-[#B4B4B4]">
              <span>{post.date}</span>
              <div className="flex items-center gap-2">
                <FileIcon />
                <span>{post.readTime}</span>
              </div>
            </div>

            {/* Author Info */}
            <div className="flex items-center">
              <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-200">
                <div className="flex h-full w-full items-center justify-center text-lg font-semibold text-gray-600">
                  {post.author.name.charAt(0).toUpperCase()}
                </div>
              </div>
              <span className="font-medium text-black">{post.author.name}</span>
            </div>
          </div>

          {/* Right Image */}
          <div className="mt-8 lg:mt-0 lg:w-1/2">
            <div className="relative h-64 overflow-hidden rounded-lg lg:h-80">
              <Image
                src={post.featuredImage}
                alt="Blog post featured image"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>

        {/* Article Content */}
        <div className="mx-auto max-w-7xl">
          <div
            className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:leading-relaxed prose-p:text-gray-700"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>

        {/* Comments Section */}
        <Comments blogId={post.id} />
      </div>
    </div>
  );
};

export default SingleBlogPost;
