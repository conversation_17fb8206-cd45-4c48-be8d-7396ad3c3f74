"use client";
import { useMemo, useState } from "react";
import { ArrowRight, Check } from "lucide-react";
import Link from "next/link";
import { calculatePrice } from "@/features/pricing/utils";
import { getRedirectUrl } from "@/lib/utils";
import { useRouter } from "next/navigation";
// type PlanType = "daily" | "subscription" | "custom";
type PlanType = "daily" | "subscription" | "custom";

const PURPLE = "#5936CD";

type PlanCardProps = {
  title: string;
  price: string;
  period: string;
  onClick: () => void;
  bgColor?: string;
  textColor?: string;
};

type CustomPlanProps = {
  onSelect: (days: number, price: number) => void;
};

export default function PricingPage({ env }: { env?: string }) {
  const router = useRouter();

  const handleSelectPlan = (
    planType: PlanType,
    price: number,
    days?: number,
  ) => {
    console.log("Selected plan:", { planType, price, days });

    const url = getRedirectUrl("register", env);
    console.log(" Redirecting to:", url);
    if (url.startsWith("http")) window.location.href = url;
    else router.push(url);
  };
  return (
    <div className="w-full">
      <div className="mx-auto max-w-7xl px-6 sm:px-8">
        <div className="my-8 text-center lg:my-12">
          <h1 className="text-3xl font-bold tracking-tighter text-black sm:text-4xl lg:text-5xl">
            Ready to ace your exams?
          </h1>
          <p className="mx-auto mt-3 max-w-2xl text-base text-gray-600 sm:text-lg">
            All exams. One subscription.
          </p>
        </div>

        <main className="flex flex-col justify-center gap-8 lg:flex-row">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <PlanCard
              title="Daily Plan"
              price="99"
              period="/day"
              onClick={() => handleSelectPlan("daily", 99)}
            />
            <PlanCard
              title="Bi-Monthly Plan"
              price="2000"
              period="/Two months"
              bgColor="bg-[#7B53FF]"
              textColor="text-white"
              onClick={() => handleSelectPlan("subscription", 2000)}
            />
            <PlanCard
              title="Monthly plan"
              price="1000"
              period="/month"
              bgColor="bg-[#5936CD]"
              textColor="text-white"
              onClick={() => handleSelectPlan("subscription", 1000)}
            />
            <CustomPlan
              onSelect={(days, price) =>
                handleSelectPlan("custom", price, days)
              }
            />
          </div>

          <aside className="relative top-8 h-fit overflow-hidden rounded-[28px] border border-white/60 bg-white/40 p-8 shadow-lg backdrop-blur-md lg:sticky lg:w-[360px]">
            <h4 className="text-center text-2xl font-bold text-black">
              Features Included
            </h4>
            <ul className="mt-8 space-y-4">
              {[
                "Unlimited mock tests (all exams)",
                "Custom quiz builder",
                "All Entry Tests covered",
                "50k+ question bank",
                "FREE 1k+ resources bank",
                "AI based analytics",
                "AI Tutorbot",
                "No extra bundles or hidden charges",
                "All services in one plan",
              ].map((service, i) => (
                <li key={i} className="flex items-center gap-3">
                  <Check size={24} color={PURPLE} />
                  <span className="text-lg font-medium text-[#334155]">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </aside>
        </main>

        <footer className="mt-12 text-center">
          <Link
            className="flex items-center justify-center gap-2 text-lg font-medium text-[#5936CD]"
            href="/register"
          >
            No thanks, I&apos;ll try it out first.
            <ArrowRight size={20} />
          </Link>
        </footer>
      </div>
    </div>
  );
}

function PlanCard({
  title,
  price,
  period,
  onClick,
  bgColor = "bg-white",
  textColor = "text-black",
}: PlanCardProps) {
  return (
    <div
      className={`flex flex-col rounded-[18px] p-5 shadow-[-4px_4px_30px_rgba(0,0,0,0.04)] ${bgColor} ${textColor} min-h-[280px]`}
    >
      <h3 className="text-2xl font-bold">{title}</h3>
      <div className="mt-4 flex items-end gap-1">
        <span className="text-4xl font-bold">Rs. {price}</span>
        <span
          className={`text-base font-semibold ${textColor === "text-white" ? "opacity-40" : "text-[#656565]"}`}
        >
          {period}
        </span>
      </div>
      <button
        onClick={onClick}
        className={`mt-auto h-12 w-full rounded-[10px] text-sm font-semibold ${textColor === "text-white" ? "bg-white text-[#1A1C1E]" : "bg-[#5936CD] text-white"}`}
        style={{
          boxShadow:
            textColor === "text-white"
              ? "inset 0px -3px 6px rgba(244, 245, 250, 0.6)"
              : "0px 1px 2px rgba(89, 54, 205, 0.48), 0px 0px 0px 1px #5936CD",
        }}
      >
        Get
      </button>
    </div>
  );
}

function CustomPlan({ onSelect }: CustomPlanProps) {
  const [days, setDays] = useState(365);
  const { total: price } = useMemo(() => calculatePrice(days), [days]);

  return (
    <div className="flex min-h-[280px] flex-col rounded-[18px] bg-gradient-to-b from-[#5936CD] to-[#2D1B67] p-5 text-white shadow-[-4px_4px_30px_rgba(0,0,0,0.04)]">
      <h3 className="text-2xl font-bold">Custom Plan</h3>
      <div className="mt-8">
        <input
          type="range"
          min={1}
          max={365}
          value={days}
          onChange={(e) => setDays(parseInt(e.target.value))}
          className="custom-slider"
        />
      </div>
      <div className="mt-4 flex items-end gap-1">
        <span className="text-4xl font-bold">
          Rs. {price.toString().padStart(4, "0")}
        </span>
        <span className="text-base font-semibold opacity-40">/{days} Days</span>
      </div>
      <button
        onClick={() => onSelect(days, price)}
        className="mt-auto h-12 w-full rounded-[10px] bg-white text-sm font-semibold text-[#1A1C1E]"
        style={{
          border: "1px solid #EFF0F6",
          boxShadow: "inset 0px -3px 6px rgba(244, 245, 250, 0.6)",
        }}
      >
        Buy
      </button>
    </div>
  );
}
