import { useMemo, useState } from "react";
import { ArrowRight, Check } from "lucide-react";
import { getRedirectUrl } from "@/lib/utils";
import { calculatePrice } from "@/features/pricing/utils";
// type PlanType = "daily" | "subscription" | "custom";
type PlanType = "daily" | "subscription" | "custom";

// const PURPLE = "#5936CD";

type PlanCardProps = {
  title: string;
  price: string;
  period: string;
  onClick: () => void;
  bgColor?: string;
  textColor?: string;
  className?: string;
};

type CustomPlanProps = {
  onSelect: (days: number, price: number) => void;
  className?: string;
};

export default function PricingPage({ env }: { env?: string }) {
  const handleSelectPlan = (
    planType: PlanType,
    price: number,
    days?: number,
  ) => {
    console.log("Selected plan:", { planType, price, days });

    const url = getRedirectUrl("register", env);
    console.log(" Redirecting to:", url);
    if (url.startsWith("http")) window.location.href = url;
    else window.location.href = url;
  };

  return (
    <div className="min-h-screen w-full">
      <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="my-6 text-center sm:my-8 lg:my-12">
          <h1 className="text-2xl font-bold tracking-tighter text-black sm:text-3xl md:text-4xl lg:text-5xl">
            Ready to ace your exams?
          </h1>
          <p className="mx-auto mt-3 max-w-2xl px-2 text-sm text-gray-600 sm:text-base lg:text-lg">
            All exams. One subscription.
          </p>
        </div>

        <main className="mt-6 flex flex-col justify-center gap-6 lg:mt-10 lg:flex-row lg:gap-8">
          {/* Mobile: Horizontal Carousel Layout */}
          <div className="flex-1 lg:hidden">
            <div className="">
              <PlanCard
                className="w-[85%] shrink-0 snap-start sm:w-[70%]"
                title="Daily Plan"
                price="99"
                period="/day"
                onClick={() => handleSelectPlan("daily", 99)}
              />
              <PlanCard
                className="w-[85%] shrink-0 snap-start sm:w-[70%]"
                title="Monthly plan"
                price="1000"
                period="/month"
                bgColor="bg-[#5936CD]"
                textColor="text-white"
                onClick={() => handleSelectPlan("subscription", 1000)}
              />
              <PlanCard
                className="w-[85%] shrink-0 snap-start sm:w-[70%]"
                title="Bi-Monthly Plan"
                price="2000"
                period="/Two months"
                bgColor="bg-[#7B53FF]"
                textColor="text-white"
                onClick={() => handleSelectPlan("subscription", 2000)}
              />
              <CustomPlan
                className="w-[85%] shrink-0 snap-start sm:w-[70%]"
                onSelect={(days, price) =>
                  handleSelectPlan("custom", price, days)
                }
              />
            </div>
          </div>

          {/* Desktop: grid */}
          <div className="hidden flex-1 grid-cols-1 gap-6 md:grid-cols-2 lg:grid">
            <PlanCard
              title="Daily Plan"
              price="99"
              period="/day"
              onClick={() => handleSelectPlan("daily", 99)}
            />
            <PlanCard
              title="Monthly plan"
              price="1000"
              period="/month"
              bgColor="bg-[#5936CD]"
              textColor="text-white"
              onClick={() => handleSelectPlan("subscription", 1000)}
            />
            <PlanCard
              title="Bi-Monthly Plan"
              price="2000"
              period="/Two months"
              bgColor="bg-[#7B53FF]"
              textColor="text-white"
              onClick={() => handleSelectPlan("subscription", 2000)}
            />
            <CustomPlan
              onSelect={(days, price) =>
                handleSelectPlan("custom", price, days)
              }
            />
          </div>

          {/* Features Included */}

          <aside
            className="mx-auto mb-8 h-auto w-full max-w-md rounded-[20px] border border-white/60 
        bg-white/40 p-4 shadow-lg backdrop-blur-md sm:max-w-lg sm:rounded-[28px] sm:p-6
        lg:sticky lg:top-8 lg:mx-0 lg:mb-0 lg:w-[360px] lg:max-w-none lg:p-8"
          >
            <h4 className="text-center text-lg font-bold text-black sm:text-xl lg:text-2xl">
              Features Included
            </h4>
            <ul className="mt-4 space-y-2 sm:mt-6 sm:space-y-3 lg:mt-8 lg:space-y-4">
              {[
                "Unlimited mock tests (all exams)",
                "Custom quiz builder",
                "All Entry Tests",
                "50k+ question bank",
                "1k+ resources bank",
                "AI based analytics",
                "AI Tutorbot",
                "No extra bundles or hidden charges",
                "All services in one plan",
              ].map((service, i) => (
                <li key={i} className="flex items-start gap-2 sm:gap-3">
                  <Check
                    size={16}
                    color="#5936CD"
                    className="mt-1 shrink-0 sm:mt-0 sm:size-5"
                  />
                  <span className="text-sm font-medium leading-relaxed text-[#334155] sm:text-base lg:text-lg">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
            <a
              className="mt-4 flex items-center justify-center gap-2 text-sm font-medium text-[#5936CD]
          transition-colors hover:text-[#4A2BA3] sm:mt-6 sm:text-base lg:mt-8 lg:text-lg"
              href="/dashboard"
            >
              No thanks, I&apos;ll try it out first.
              <ArrowRight size={16} className="shrink-0 sm:size-[18px]" />
            </a>
          </aside>
        </main>
      </div>
    </div>
  );
}

function PlanCard({
  title,
  price,
  period,
  onClick,
  bgColor = "bg-white",
  textColor = "text-black",
  className = "",
}: PlanCardProps) {
  return (
    <div
      className={`flex flex-col rounded-[16px] p-4 shadow-[-4px_4px_30px_rgba(0,0,0,0.04)] sm:rounded-[18px] sm:p-5 ${bgColor} ${textColor} min-h-[240px] transform transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg sm:min-h-[260px] ${className}`}
    >
      <h3 className="text-lg font-bold leading-tight sm:text-xl md:text-2xl">
        {title}
      </h3>
      <div className="mt-3 flex items-end gap-1 sm:mt-4">
        <span className="text-2xl font-bold sm:text-3xl md:text-4xl">
          Rs. {price}
        </span>
        <span
          className={`text-sm font-semibold sm:text-base ${
            textColor === "text-white" ? "opacity-40" : "text-[#656565]"
          }`}
        >
          {period}
        </span>
      </div>
      <button
        onClick={onClick}
        className={`mt-auto h-11 w-full transform rounded-[8px] text-sm font-semibold transition-all duration-200 hover:scale-[1.02] active:scale-95 sm:h-12 sm:rounded-[10px] ${
          textColor === "text-white"
            ? "bg-white text-[#1A1C1E] hover:bg-gray-50"
            : "bg-[#5936CD] text-white hover:bg-[#4A2BA3]"
        }`}
        style={{
          boxShadow:
            textColor === "text-white"
              ? "inset 0px -3px 6px rgba(244, 245, 250, 0.6)"
              : "0px 1px 2px rgba(89, 54, 205, 0.48), 0px 0px 0px 1px #5936CD",
        }}
      >
        Get
      </button>
    </div>
  );
}

function CustomPlan({ onSelect, className = "" }: CustomPlanProps) {
  const [days, setDays] = useState(365);
  const { total: price } = useMemo(() => calculatePrice(days), [days]);

  return (
    <div
      className={`flex min-h-[240px] transform flex-col rounded-[16px] bg-gradient-to-b from-[#5936CD] to-[#2D1B67] p-4 text-white shadow-[-4px_4px_30px_rgba(0,0,0,0.04)] transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg sm:min-h-[260px] sm:rounded-[18px] sm:p-5 ${className}`}
    >
      <h3 className="text-lg font-bold leading-tight sm:text-xl md:text-2xl">
        Custom Plan
      </h3>
      <div
        className="mt-6 px-1 sm:mt-8"
        onTouchMove={(e) => e.stopPropagation()}
      >
        <input
          type="range"
          min={1}
          max={365}
          value={days}
          onChange={(e) => setDays(parseInt(e.target.value))}
          className="custom-slider w-full"
        />
      </div>
      <div className="mt-3 flex items-end gap-1 sm:mt-4">
        <span className="text-2xl font-bold sm:text-3xl md:text-4xl">
          Rs. {price.toString().padStart(4, "0")}
        </span>
        <span className="text-sm font-semibold opacity-40 sm:text-base">
          /{days} Days
        </span>
      </div>
      <button
        onClick={() => onSelect(days, price)}
        className="mt-auto h-11 w-full transform rounded-[8px] bg-white text-sm font-semibold text-[#1A1C1E] transition-all duration-200 hover:scale-[1.02] hover:bg-gray-50 active:scale-95 sm:h-12 sm:rounded-[10px]"
        style={{
          border: "1px solid #EFF0F6",
          boxShadow: "inset 0px -3px 6px rgba(244, 245, 250, 0.6)",
        }}
      >
        Buy
      </button>
    </div>
  );
}
