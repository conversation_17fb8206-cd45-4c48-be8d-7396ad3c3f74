import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Check } from "lucide-react";

type PricingCardProps = {
  title: string;
  price?: number; // Optional for custom plans
  period?: string; // Optional for custom plans
  features: string[];
  isPrimary?: boolean;
  isCustom?: boolean; // New prop to indicate custom plan
  onGetPlan?: (days?: number, price?: number) => void; // Updated to support custom plan params
};

const PricingCard = ({
  title,
  price,
  period,
  features,
  isPrimary = false,
  isCustom = false,
  onGetPlan = () => {},
}: PricingCardProps) => {
  // State for custom plan slider
  const [days, setDays] = useState(365);

  // Calculate price for custom plans
  const calculateCustomPrice = (days: number) => {
    return Math.round((days / 365) * 1000);
  };

  // Get the actual price and period to display
  const displayPrice = isCustom ? calculateCustomPrice(days) : price;
  const displayPeriod = isCustom ? `${days} Days` : period;

  // Handle button click
  const handleGetPlan = () => {
    if (isCustom) onGetPlan(days, displayPrice);
    else onGetPlan();
  };
  return (
    <Card
      className={`mb:py-14 mb:px-10 w-full rounded-xl px-8 py-10 shadow-lg md:w-[370px] ${isCustom ? "bg-gradient-to-b from-accent to-[#2D1B67] text-white" : isPrimary ? "bg-accent text-white" : "bg-white"} flex flex-col`}
    >
      <CardHeader className="space-y-[15px] p-0">
        <h3 className="text-xl font-bold md:text-2xl">{title}</h3>

        {/* Slider for custom plans */}
        {isCustom && (
          <div className="pt-2.5">
            <Slider
              value={days}
              onChange={setDays}
              min={1}
              max={365}
              step={1}
              className="slider-thumb:bg-[#D9D9D9] slider-thumb:shadow-[0px_4px_4px_0px_#00000040] h-[3px] bg-[#D9D9D9] shadow-[0px_4px_4px_0px_#00000040] [&::-moz-range-thumb]:bg-[#D9D9D9] [&::-moz-range-thumb]:shadow-[0px_4px_4px_0px_#00000040] [&::-moz-range-track]:rounded-lg [&::-moz-range-track]:bg-[#D9D9D9] [&::-webkit-slider-thumb]:bg-[#D9D9D9] [&::-webkit-slider-thumb]:shadow-[0px_4px_4px_0px_#00000040] [&::-webkit-slider-track]:rounded-lg [&::-webkit-slider-track]:bg-[#D9D9D9]"
            />
          </div>
        )}

        <div className="flex items-end gap-1 leading-[38px] md:leading-[42px]">
          <span className="text-sm font-bold md:text-xl">Rs.</span>
          <span className="text-[40px] font-bold md:text-5xl">
            {displayPrice?.toLocaleString()}
          </span>
          <span
            className={`text-sm font-semibold md:text-base ${
              isCustom || isPrimary ? "text-[#FFFFFF66]" : "text-[#656565]"
            }`}
          >
            /{displayPeriod}
          </span>
        </div>
      </CardHeader>

      <CardContent className="mb:py-12 flex-grow space-y-6 p-0 py-10">
        {features.map((feature, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className="p-1">
              <Check
                className={`h-4 w-4 ${
                  isCustom || isPrimary ? "text-white" : "text-accent"
                }`}
              />
            </div>
            <span
              className={`text-sm ${isCustom || isPrimary ? "" : "text-gray-700"}`}
            >
              {feature}
            </span>
          </div>
        ))}
      </CardContent>

      <CardFooter className="mt-auto p-0">
        <Button
          onClick={handleGetPlan}
          className={`w-full py-6 ${
            isCustom || isPrimary
              ? "bg-white text-black hover:bg-gray-100"
              : "bg-accent text-white"
          }`}
        >
          Get
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PricingCard;
