import LabelBanner from "@/components/common/LabelBanner";
import AggregateCalculatorContent from "@/components/landing/aggregate/AggregateCalculatorContent";
import React from "react";

const AggregateCalculatorPage = () => {
  return (
    <div className="mt-32 flex w-full flex-col items-center justify-center space-y-16 md:mt-36 lg:mt-0 lg:space-y-24">
      <LabelBanner
        label="Aggregate Calculator"
        iconUrl="/assets/icons/calculator.svg"
      />
      <AggregateCalculatorContent />
    </div>
  );
};

export default AggregateCalculatorPage;
