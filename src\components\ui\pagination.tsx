import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

const Pagination = ({ className, ...props }: React.ComponentProps<"nav">) => (
	<nav
		role="navigation"
		aria-label="pagination"
		className={cn("mx-auto flex w-full justify-center", className)}
		{...props}
	/>
);
Pagination.displayName = "Pagination";

const PaginationContent = React.forwardRef<
	HTMLUListElement,
	React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
	<ul
		ref={ref}
		className={cn("flex flex-row items-center gap-2 px-3 py-2", className)}
		{...props}
	/>
));
PaginationContent.displayName = "PaginationContent";

const PaginationItem = React.forwardRef<
	HTMLLIElement,
	React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
	<li ref={ref} className={cn("", className)} {...props} />
));
PaginationItem.displayName = "PaginationItem";

type PaginationLinkProps = {
	isActive?: boolean;
} & React.ComponentProps<"button">;

const PaginationLink = ({
	className,
	isActive,
	...props
}: PaginationLinkProps) => (
	<button
		aria-current={isActive ? "page" : undefined}
		className={cn(
			"h-8 w-8 flex items-center justify-center text-sm font-medium rounded transition-colors",
			isActive
				? "bg-accent text-primary-foreground"
				: "text-gray-600 hover:text-gray-900 hover:bg-gray-100",
			className
		)}
		{...props}
	/>
);
PaginationLink.displayName = "PaginationLink";

const PaginationPrevious = ({
	className,
	...props
}: React.ComponentProps<"button">) => (
	<button
		aria-label="Go to previous page"
		className={cn(
			"h-8 w-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors",
			className
		)}
		{...props}
	>
		<ChevronLeft className="h-4 w-4" />
	</button>
);
PaginationPrevious.displayName = "PaginationPrevious";

const PaginationNext = ({
	className,
	...props
}: React.ComponentProps<"button">) => (
	<button
		aria-label="Go to next page"
		className={cn(
			"h-8 w-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors",
			className
		)}
		{...props}
	>
		<ChevronRight className="h-4 w-4" />
	</button>
);
PaginationNext.displayName = "PaginationNext";

const PaginationEllipsis = ({
	className,
	...props
}: React.ComponentProps<"span">) => (
	<span
		aria-hidden
		className={cn("flex h-8 w-8 items-center justify-center text-gray-500", className)}
		{...props}
	>
		<span>...</span>
		<span className="sr-only">More pages</span>
	</span>
);
PaginationEllipsis.displayName = "PaginationEllipsis";

export {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
};
